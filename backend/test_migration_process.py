#!/usr/bin/env python3
"""
Test script to verify the complete migration process works from scratch.

This script:
1. Resets the database and deletes migration files
2. Creates new migration files
3. Starts docker-compose to test the migration process
4. Checks if the process completed successfully
"""

import subprocess
import sys
import time
import os

def run_command(command, cwd=None, check=True):
    """Run a command and return the result."""
    print(f"Running: {command}")
    result = subprocess.run(
        command,
        shell=True,
        cwd=cwd,
        capture_output=True,
        text=True
    )
    
    if check and result.returncode != 0:
        print(f"Command failed with return code {result.returncode}")
        print(f"STDOUT: {result.stdout}")
        print(f"STDERR: {result.stderr}")
        sys.exit(1)
    
    return result

def main():
    """Main test function."""
    backend_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(backend_dir)
    
    print("=" * 60)
    print("TESTING COMPLETE MIGRATION PROCESS FROM SCRATCH")
    print("=" * 60)
    
    # Step 1: Reset database and delete migration files
    print("\n1. Resetting database and deleting migration files...")
    run_command(
        f"python {backend_dir}/apps/utils/reset_db.py --delete-migrations",
        cwd=project_root
    )
    
    # Step 2: Stop any running containers
    print("\n2. Stopping any running containers...")
    run_command("docker-compose down", cwd=backend_dir, check=False)
    
    # Step 3: Create new migration files
    print("\n3. Creating new migration files...")
    # Start containers temporarily to create migrations
    run_command("docker-compose up -d db redis", cwd=backend_dir)
    time.sleep(5)  # Wait for DB to be ready
    
    # Create migrations using the web container
    run_command("docker-compose run --rm web python manage.py makemigrations", cwd=backend_dir)
    
    # Stop the temporary containers
    run_command("docker-compose down", cwd=backend_dir)
    
    # Step 4: Test the complete startup process
    print("\n4. Testing complete startup process...")
    run_command("docker-compose up -d", cwd=backend_dir)
    
    # Step 5: Wait and check logs
    print("\n5. Waiting for startup to complete...")
    time.sleep(30)  # Wait for startup to complete
    
    # Check if web container is running
    result = run_command("docker-compose ps web", cwd=backend_dir, check=False)
    if "Up" not in result.stdout:
        print("ERROR: Web container is not running!")
        run_command("docker-compose logs web", cwd=backend_dir, check=False)
        sys.exit(1)
    
    # Check logs for migration success
    logs_result = run_command("docker-compose logs web", cwd=backend_dir, check=False)
    
    if "Running idempotent seeding commands..." in logs_result.stdout:
        print("✅ SUCCESS: Migrations completed and seeding started!")
    else:
        print("❌ ERROR: Migration process may have failed!")
        print("Web container logs:")
        print(logs_result.stdout[-2000:])  # Last 2000 characters
        sys.exit(1)
    
    # Step 6: Verify database tables exist
    print("\n6. Verifying database tables exist...")
    tables_result = run_command(
        "docker exec backend-db-1 psql -U postgres -d mydb -c \"\\dt\"",
        cwd=backend_dir,
        check=False
    )
    
    if "activity_" in tables_result.stdout and "user_" in tables_result.stdout and "main_" in tables_result.stdout:
        print("✅ SUCCESS: Database tables created successfully!")
    else:
        print("❌ ERROR: Expected database tables not found!")
        print(tables_result.stdout)
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("🎉 ALL TESTS PASSED! Migration process works correctly from scratch.")
    print("=" * 60)
    
    print("\nTo clean up, run: docker-compose down")

if __name__ == "__main__":
    main()
