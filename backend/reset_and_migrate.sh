#!/bin/bash

# Reset and Migrate Script
# This script provides a clean way to reset the database and migrations, then restart the application

set -e  # Exit on any error

echo "============================================================"
echo "GOALI DATABASE RESET AND MIGRATION SCRIPT"
echo "============================================================"

# Check if we're in the backend directory
if [ ! -f "docker-compose.yml" ]; then
    echo "Error: This script must be run from the backend directory"
    exit 1
fi

echo ""
echo "⚠️  WARNING: This will:"
echo "   - Delete ALL data in the database"
echo "   - Delete ALL migration files"
echo "   - Recreate migrations from scratch"
echo "   - Restart the application"
echo ""

# Ask for confirmation unless --force flag is provided
if [ "$1" != "--force" ]; then
    read -p "Are you sure you want to continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Operation cancelled."
        exit 0
    fi
fi

echo ""
echo "🔄 Step 1: Stopping containers..."
docker-compose down

echo ""
echo "🗑️  Step 2: Resetting database and deleting migration files..."
python apps/utils/reset_db.py --delete-migrations

echo ""
echo "📝 Step 3: Creating new migration files..."
# Start only the database to create migrations
docker-compose up -d db redis
sleep 5  # Wait for database to be ready

# Create migrations
docker-compose run --rm web python manage.py makemigrations

# Stop temporary containers
docker-compose down

echo ""
echo "🚀 Step 4: Starting application with fresh migrations..."
docker-compose up -d

echo ""
echo "⏳ Waiting for application to start..."
sleep 10

# Check if containers are running
if docker-compose ps | grep -q "Up"; then
    echo ""
    echo "✅ SUCCESS! Application is starting up."
    echo ""
    echo "📋 You can check the progress with:"
    echo "   docker-compose logs -f web"
    echo ""
    echo "🌐 Once startup is complete, the application will be available at:"
    echo "   http://localhost:8000"
    echo ""
    echo "🛑 To stop the application:"
    echo "   docker-compose down"
else
    echo ""
    echo "❌ ERROR: Containers failed to start. Check logs:"
    echo "   docker-compose logs"
    exit 1
fi

echo ""
echo "============================================================"
echo "🎉 RESET AND MIGRATION COMPLETE!"
echo "============================================================"
