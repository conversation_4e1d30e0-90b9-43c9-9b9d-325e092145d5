#!/usr/bin/env python3
"""
Migration Conflict Resolution Script for Goali Project

This script helps resolve the current migration conflicts in the main app
where multiple 0002_* migrations exist.
"""

import os
import sys
import django
from django.conf import settings
from django.core.management import execute_from_command_line
from django.db import connection
from django.db.migrations.recorder import MigrationRecorder

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

def check_migration_state():
    """Check current migration state and identify conflicts."""
    print("🔍 Checking current migration state...")
    
    recorder = MigrationRecorder(connection)
    applied_migrations = recorder.applied_migrations()
    
    # Check for main app migrations
    main_migrations = [m for m in applied_migrations if m[0] == 'main']
    print(f"\n📋 Applied migrations in 'main' app ({len(main_migrations)}):")
    for app, migration in sorted(main_migrations):
        print(f"  ✅ {app}.{migration}")
    
    # Check for 0002_* conflicts
    zero_two_migrations = [m for m in main_migrations if m[1].startswith('0002_')]
    if len(zero_two_migrations) > 1:
        print(f"\n⚠️  Found {len(zero_two_migrations)} migrations starting with 0002_:")
        for app, migration in zero_two_migrations:
            print(f"  🔄 {app}.{migration}")
        return True
    
    print("\n✅ No 0002_* conflicts found")
    return False

def check_merge_migration():
    """Check if merge migration exists and its dependencies."""
    merge_file = "backend/apps/main/migrations/0003_merge_20250426_1126.py"
    if os.path.exists(merge_file):
        print(f"\n📄 Found merge migration: {merge_file}")
        with open(merge_file, 'r') as f:
            content = f.read()
            if 'dependencies' in content:
                print("📋 Dependencies found in merge migration")
                return True
    return False

def suggest_resolution():
    """Suggest resolution steps."""
    print("\n🔧 RECOMMENDED RESOLUTION STEPS:")
    print("=" * 50)
    
    print("\n1. 📊 ASSESSMENT:")
    print("   - Multiple 0002_* migrations in main app")
    print("   - Merge migration 0003_merge_20250426_1126.py exists")
    print("   - Potential dependency conflicts")
    
    print("\n2. 🛠️  RESOLUTION OPTIONS:")
    print("\n   OPTION A: Reset and Recreate (SAFEST for development)")
    print("   - Backup current database")
    print("   - Delete all migration files")
    print("   - Recreate migrations from current models")
    print("   - Apply fresh migrations")
    
    print("\n   OPTION B: Fix Dependencies (for production)")
    print("   - Ensure all 0002_* migrations are applied")
    print("   - Verify merge migration dependencies")
    print("   - Use fix_migration_state command if needed")
    
    print("\n3. 📝 PREVENTION:")
    print("   - Always run 'makemigrations --merge' for conflicts")
    print("   - Coordinate with team on migration timing")
    print("   - Use descriptive migration names")
    print("   - Test migrations on staging first")

def main():
    """Main execution function."""
    print("🚀 Goali Migration Conflict Resolution")
    print("=" * 40)
    
    try:
        # Check if we can connect to database
        connection.ensure_connection()
        print("✅ Database connection successful")
        
        # Check migration state
        has_conflicts = check_migration_state()
        
        # Check merge migration
        has_merge = check_merge_migration()
        
        # Provide recommendations
        if has_conflicts or has_merge:
            suggest_resolution()
        else:
            print("\n✅ No migration conflicts detected!")
            print("💡 Your migration state appears to be clean.")
        
    except Exception as e:
        print(f"❌ Error checking migration state: {e}")
        print("💡 Make sure your database is running and accessible")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
