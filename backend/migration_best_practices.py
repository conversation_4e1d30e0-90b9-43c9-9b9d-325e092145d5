#!/usr/bin/env python3
"""
Migration Best Practices Helper for Goali Project

This script provides utilities for managing migrations according to Django best practices.
"""

import os
import sys
import subprocess
import json
from datetime import datetime
from pathlib import Path

class MigrationManager:
    """Helper class for managing Django migrations."""
    
    def __init__(self, project_root="backend"):
        self.project_root = Path(project_root)
        self.apps = ['admin_tools', 'user', 'activity', 'main']
    
    def run_command(self, command, capture_output=True):
        """Run a Django management command."""
        try:
            result = subprocess.run(
                command,
                shell=True,
                cwd=self.project_root,
                capture_output=capture_output,
                text=True,
                check=True
            )
            return result.stdout if capture_output else None
        except subprocess.CalledProcessError as e:
            print(f"❌ Command failed: {command}")
            print(f"Error: {e.stderr if e.stderr else e}")
            return None
    
    def check_migration_status(self):
        """Check the status of all migrations."""
        print("🔍 Checking migration status...")
        
        output = self.run_command("python manage.py showmigrations")
        if output:
            print(output)
            return True
        return False
    
    def create_migration_plan(self):
        """Create a migration plan showing what will be applied."""
        print("📋 Creating migration plan...")
        
        output = self.run_command("python manage.py migrate --plan")
        if output:
            print("Migration Plan:")
            print(output)
            return True
        return False
    
    def validate_migrations(self):
        """Validate all migrations for consistency."""
        print("✅ Validating migrations...")
        
        # Check for migration conflicts
        for app in self.apps:
            migrations_dir = self.project_root / "apps" / app / "migrations"
            if migrations_dir.exists():
                migration_files = list(migrations_dir.glob("*.py"))
                migration_files = [f for f in migration_files if not f.name.startswith("__")]
                
                # Check for duplicate numbers
                numbers = []
                for migration_file in migration_files:
                    if migration_file.name[0].isdigit():
                        number = migration_file.name.split('_')[0]
                        if number in numbers:
                            print(f"⚠️  Duplicate migration number {number} in {app}")
                        numbers.append(number)
    
    def backup_database(self):
        """Create a database backup before migrations."""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_file = f"backup_before_migration_{timestamp}.sql"
        
        print(f"💾 Creating database backup: {backup_file}")
        
        # This would need to be adapted based on your database setup
        command = f"docker exec backend-db-1 pg_dump -U postgres mydb > {backup_file}"
        result = self.run_command(command, capture_output=False)
        
        if result is not None:
            print(f"✅ Backup created: {backup_file}")
            return backup_file
        else:
            print("❌ Backup failed")
            return None
    
    def safe_migrate(self, app=None):
        """Perform a safe migration with backup and validation."""
        print("🚀 Starting safe migration process...")
        
        # Step 1: Validate current state
        if not self.check_migration_status():
            print("❌ Migration status check failed")
            return False
        
        # Step 2: Create backup
        backup_file = self.backup_database()
        if not backup_file:
            response = input("⚠️  Backup failed. Continue anyway? (y/N): ")
            if response.lower() != 'y':
                return False
        
        # Step 3: Show migration plan
        if not self.create_migration_plan():
            print("❌ Could not create migration plan")
            return False
        
        # Step 4: Confirm with user
        response = input("🤔 Proceed with migration? (y/N): ")
        if response.lower() != 'y':
            print("❌ Migration cancelled by user")
            return False
        
        # Step 5: Apply migrations
        command = "python manage.py migrate"
        if app:
            command += f" {app}"
        
        print(f"🔄 Running: {command}")
        result = self.run_command(command, capture_output=False)
        
        if result is not None:
            print("✅ Migration completed successfully!")
            return True
        else:
            print("❌ Migration failed!")
            if backup_file:
                print(f"💡 Restore from backup: {backup_file}")
            return False

def main():
    """Main execution function."""
    print("🛠️  Django Migration Best Practices Helper")
    print("=" * 45)
    
    manager = MigrationManager()
    
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python migration_best_practices.py status    - Check migration status")
        print("  python migration_best_practices.py validate  - Validate migrations")
        print("  python migration_best_practices.py plan     - Show migration plan")
        print("  python migration_best_practices.py migrate  - Safe migrate all")
        print("  python migration_best_practices.py migrate <app> - Safe migrate specific app")
        return 1
    
    command = sys.argv[1]
    
    if command == "status":
        manager.check_migration_status()
    elif command == "validate":
        manager.validate_migrations()
    elif command == "plan":
        manager.create_migration_plan()
    elif command == "migrate":
        app = sys.argv[2] if len(sys.argv) > 2 else None
        manager.safe_migrate(app)
    else:
        print(f"❌ Unknown command: {command}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
