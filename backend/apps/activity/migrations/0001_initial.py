# Generated by Django 5.2.1 on 2025-07-08 12:13

import apps.common.fields
import django.core.validators
import django.db.models.deletion
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='ActivityEnvRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('env_type', models.CharField(help_text="General category of environment needed (e.g., 'Indoor', 'Outdoor', 'Social', 'Private').", max_length=100)),
                ('env_detail', models.JSONField(help_text='Detailed specifications about the environmental requirements including conditions and characteristics.')),
                ('required_level', apps.common.fields.ValidatedRangeField(help_text='How strictly (0-100) the environmental conditions must be met for the activity to be viable.', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('optional', models.BooleanField(default=False, help_text='Whether this environmental requirement is essential (False) or preferable but not required (True).')),
            ],
            options={
                'ordering': ['env_type'],
            },
        ),
        migrations.CreateModel(
            name='ActivityTailored',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='The name of the activity.', max_length=255)),
                ('description', models.TextField(help_text='A brief summary of the activity.')),
                ('created_on', models.DateField(help_text='Date the activity was added.')),
                ('duration_range', models.CharField(help_text='Time range for the activity.', max_length=50)),
                ('instructions', models.TextField(blank=True, help_text='Step-by-step directions for the activity.')),
                ('social_requirements', models.JSONField(help_text='JSON specifying social requirements.')),
                ('base_challenge_rating', models.IntegerField(help_text="Overall difficulty level from 0-100, calibrated for the user's current trust level and growth stage.")),
                ('challengingness', models.JSONField(help_text='JSON mapping challenge levels across the Big Five personality dimensions for gap analysis.')),
                ('version', models.IntegerField(help_text='Sequential version number tracking revisions to this tailored activity.')),
                ('tailorization_level', models.IntegerField(help_text='Metric from 0-100 indicating how extensively the activity was customized from its generic template.')),
            ],
            options={
                'ordering': ['-created_on'],
            },
        ),
        migrations.CreateModel(
            name='ActivityTailoredQueryIndexes',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('user_profile_id', models.UUIDField(db_index=True, help_text='Indexed foreign key to user profile for faster joins')),
                ('challenge_score', models.IntegerField(db_index=True, help_text='Pre-computed overall challenge score (0-100)')),
                ('domain_primary', models.CharField(db_index=True, help_text='Primary domain code for this activity', max_length=50)),
                ('domain_categories', models.JSONField(help_text='Mapping of domain categories to relevance scores (0-100)')),
                ('hexaco_profile', models.JSONField(help_text="Activity's HEXACO trait requirements as a score mapping (0-100)")),
                ('suitable_environments', models.JSONField(help_text='List of environment UUIDs with compatibility scores (0-100)')),
                ('last_updated', models.DateTimeField(auto_now=True, help_text='When this index was last refreshed')),
            ],
            options={
                'verbose_name': 'Activity Query Index',
                'verbose_name_plural': 'Activity Query Indexes',
            },
        ),
        migrations.CreateModel(
            name='ActivityTailoredResourceRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_required', models.IntegerField(help_text='Specific amount of the personal resource needed for this tailored activity.')),
                ('optional', models.BooleanField(default=False, help_text='Whether this resource is mandatory (False) or beneficial but not essential (True).')),
            ],
            options={
                'ordering': ['activity_tailored'],
            },
        ),
        migrations.CreateModel(
            name='ActivityUserRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.UUIDField(help_text='UUID of the specific user resource.')),
                ('required_level', apps.common.fields.ValidatedRangeField(help_text='Minimum proficiency level (0-100) needed for the activity to be appropriate.', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('quantity_required', models.IntegerField(help_text='Number of instances or amount of the resource needed.')),
                ('optional', models.BooleanField(default=False, help_text='Whether having this resource is mandatory (False) or beneficial but not required (True).')),
            ],
            options={
                'ordering': ['activity_tailored', 'content_type', 'object_id'],
            },
        ),
        migrations.CreateModel(
            name='EntityDomainRelationship',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.CharField(max_length=255)),
                ('strength', models.PositiveSmallIntegerField(choices=[(10, 'Minimal'), (30, 'Moderate'), (70, 'Significant'), (100, 'Primary')], default=30, help_text='How strongly this activity relates to this domain')),
            ],
            options={
                'verbose_name': 'Entity-Domain Relationship',
                'verbose_name_plural': 'Entity-Domain Relationships',
            },
        ),
        migrations.CreateModel(
            name='GenericActivity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='The name of the activity.', max_length=255)),
                ('description', models.TextField(help_text='A brief summary of the activity.')),
                ('created_on', models.DateField(help_text='Date the activity was added.')),
                ('duration_range', models.CharField(help_text='Time range for the activity.', max_length=50)),
                ('instructions', models.TextField(blank=True, help_text='Step-by-step directions for the activity.')),
                ('social_requirements', models.JSONField(help_text='JSON specifying social requirements.')),
                ('code', models.CharField(help_text="Unique, human-readable code for this activity (e.g., 'refl_mind_walking')", max_length=50, unique=True)),
                ('metadata', models.JSONField(blank=True, default=dict, help_text='Versioned metadata containing energy levels, duration constraints, and other selection criteria')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='GenericActivityEnvRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('optional', models.BooleanField(default=False, help_text='Whether this environmental requirement is mandatory (False) or preferable but not essential (True).')),
            ],
            options={
                'ordering': ['generic_activity'],
            },
        ),
        migrations.CreateModel(
            name='GenericActivityResourceRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity_required', models.IntegerField(help_text='Numerical quantity of the resource needed to complete the activity.')),
                ('optional', models.BooleanField(default=False, help_text='Whether the resource is required (False) or merely suggested (True) for activity completion.')),
            ],
            options={
                'ordering': ['generic_activity'],
            },
        ),
        migrations.CreateModel(
            name='GenericActivityUserRequirement',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.PositiveIntegerField(help_text='The ID of the specific entity instance.')),
                ('level_required', apps.common.fields.ValidatedRangeField(blank=True, help_text='Minimum level (0-100) needed for the activity, used for gap analysis.', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('optional', models.BooleanField(default=False, help_text='Whether this requirement is essential (False) or helpful but not required (True).')),
                ('impact_type', models.CharField(choices=[('requirement', 'Requirement'), ('enhancement', 'Enhancement'), ('limitation', 'Limitation'), ('adaptation', 'Adaptation')], default='requirement', help_text='How this entity impacts the activity.', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Additional context, adaptation suggestions, or other relevant notes.')),
            ],
            options={
                'verbose_name': 'Generic Activity User Requirement',
                'verbose_name_plural': 'Generic Activity User Requirements',
                'ordering': ['generic_activity', 'content_type', 'object_id'],
            },
        ),
        migrations.CreateModel(
            name='GenericActivityUserRequirementSummary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('difficulty_rating', apps.common.fields.ValidatedRangeField(help_text='Overall difficulty rating (0-100) based on all requirements.', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('requirements_summary', models.JSONField(help_text='Summary of all requirements categorized by type.')),
                ('limitations_summary', models.JSONField(help_text='Summary of how limitations affect this activity and available adaptations.')),
                ('last_updated', models.DateTimeField(auto_now=True, help_text='When this summary was last updated.')),
            ],
            options={
                'verbose_name': 'Generic Activity User Requirement Summary',
                'verbose_name_plural': 'Generic Activity User Requirement Summaries',
                'ordering': ['generic_activity'],
            },
        ),
        migrations.CreateModel(
            name='GenericDomain',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text="Unique identifier code for the domain (e.g., 'creative_artistic')", max_length=50, unique=True)),
                ('name', models.CharField(help_text="Display name of the domain (e.g., 'Artistic Creation')", max_length=100)),
                ('description', models.TextField(blank=True, help_text='Detailed description of the domain and associated activities')),
                ('primary_category', models.CharField(blank=True, choices=[('physical', 'Physical'), ('social', 'Social'), ('creative', 'Creative'), ('intellectual', 'Intellectual'), ('reflective', 'Reflective'), ('emotional', 'Emotional'), ('spiritual_existential', 'Spiritual/Existential'), ('exploratory_adventurous', 'Exploratory/Adventurous'), ('productive_practical', 'Productive/Practical'), ('leisure_recreational', 'Leisure/Recreational')], help_text='Primary category - only applicable for top-level domains', max_length=30)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Generic Domain',
                'verbose_name_plural': 'Generic Domains',
                'ordering': ['primary_category', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Tag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True)),
                ('slug', models.SlugField(max_length=100, unique=True)),
                ('description', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='TaggedItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.CharField(max_length=255)),
            ],
        ),
        migrations.CreateModel(
            name='ActivityInfluencedBy',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('object_id', models.UUIDField(help_text='UUID of the influencing entity')),
                ('influence_strength', apps.common.fields.ValidatedRangeField(help_text='Numerical rating from 0-100 indicating how strongly this entity shaped the activity design.', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('note', models.TextField(help_text="Explanation of how the referenced entity influenced the activity's design, challenge level, or framing.")),
                ('content_type', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype')),
            ],
        ),
    ]
