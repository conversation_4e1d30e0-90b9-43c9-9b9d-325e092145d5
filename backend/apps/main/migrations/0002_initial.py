# Generated by Django 5.2.1 on 2025-07-08 12:13

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('activity', '0002_initial'),
        ('contenttypes', '0002_remove_content_type_name'),
        ('main', '0001_initial'),
        ('user', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AddField(
            model_name='agentmemory',
            name='user_profile',
            field=models.ForeignKey(help_text='The user this memory is associated with', on_delete=django.db.models.deletion.CASCADE, related_name='agent_memories', to='user.userprofile'),
        ),
        migrations.AddField(
            model_name='agentrun',
            name='user_profile',
            field=models.ForeignKey(help_text='The user this agent run was performed for', on_delete=django.db.models.deletion.CASCADE, related_name='agent_runs', to='user.userprofile'),
        ),
        migrations.AddField(
            model_name='agentrecommendation',
            name='created_by_run',
            field=models.ForeignKey(help_text='The agent run that created this recommendation', on_delete=django.db.models.deletion.CASCADE, related_name='recommendations', to='main.agentrun'),
        ),
        migrations.AddField(
            model_name='agentrecommendation',
            name='reviewed_by',
            field=models.ForeignKey(blank=True, help_text='The agent run that reviewed this recommendation', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='reviewed_recommendations', to='main.agentrun'),
        ),
        migrations.AddField(
            model_name='agentmetric',
            name='agent_run',
            field=models.ForeignKey(help_text='The agent run this metric is for', on_delete=django.db.models.deletion.CASCADE, related_name='metrics', to='main.agentrun'),
        ),
        migrations.AddField(
            model_name='agentmemory',
            name='created_by_run',
            field=models.ForeignKey(blank=True, help_text='The agent run that created this memory', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_memories', to='main.agentrun'),
        ),
        migrations.AddIndex(
            model_name='agenttool',
            index=models.Index(fields=['code'], name='main_agentt_code_0b6631_idx'),
        ),
        migrations.AddIndex(
            model_name='agenttool',
            index=models.Index(fields=['is_active'], name='main_agentt_is_acti_179edc_idx'),
        ),
        migrations.AddIndex(
            model_name='agenttool',
            index=models.Index(fields=['definition_hash'], name='main_agentt_definit_2dc96a_idx'),
        ),
        migrations.AddIndex(
            model_name='appliedseedingcommand',
            index=models.Index(fields=['command_name'], name='main_applie_command_0d8ccc_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkmetric',
            index=models.Index(fields=['code'], name='main_benchm_code_dd52ca_idx'),
        ),
        migrations.AddField(
            model_name='agentmetric',
            name='metric',
            field=models.ForeignKey(help_text='The metric being measured', on_delete=django.db.models.deletion.PROTECT, related_name='measurements', to='main.benchmarkmetric'),
        ),
        migrations.AddField(
            model_name='benchmarkrun',
            name='compared_to_run',
            field=models.ForeignKey(blank=True, help_text='Reference to the previous run used for statistical comparison.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='comparisons', to='main.benchmarkrun'),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='parent_scenario',
            field=models.ForeignKey(blank=True, help_text='Link to the previous version of this scenario, if any.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='child_scenarios', to='main.benchmarkscenario'),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='variations',
            field=models.ManyToManyField(blank=True, help_text='Link to other scenarios that this one is considered a variation of.', related_name='base_scenario_set', to='main.benchmarkscenario'),
        ),
        migrations.AddField(
            model_name='benchmarkrun',
            name='scenario',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='runs', to='main.benchmarkscenario'),
        ),
        migrations.AddField(
            model_name='benchmarkscenario',
            name='tags',
            field=models.ManyToManyField(blank=True, help_text='Tags for organizing and filtering scenarios.', related_name='scenarios', to='main.benchmarktag'),
        ),
        migrations.AddField(
            model_name='betasignup',
            name='processed_by',
            field=models.ForeignKey(blank=True, help_text='Staff member who processed this request', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_beta_signups', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='customagent',
            name='user_profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_agents', to='user.userprofile'),
        ),
        migrations.AddField(
            model_name='agentgoal',
            name='custom_agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='agent_goals', to='main.customagent'),
        ),
        migrations.AddIndex(
            model_name='evaluationcriteriatemplate',
            index=models.Index(fields=['name'], name='main_evalua_name_f4e05c_idx'),
        ),
        migrations.AddIndex(
            model_name='evaluationcriteriatemplate',
            index=models.Index(fields=['workflow_type'], name='main_evalua_workflo_e78bd9_idx'),
        ),
        migrations.AddIndex(
            model_name='evaluationcriteriatemplate',
            index=models.Index(fields=['category'], name='main_evalua_categor_519c89_idx'),
        ),
        migrations.AddIndex(
            model_name='evaluationcriteriatemplate',
            index=models.Index(fields=['is_active'], name='main_evalua_is_acti_961a78_idx'),
        ),
        migrations.AddField(
            model_name='genericagent',
            name='available_tools',
            field=models.ManyToManyField(help_text='Tools this agent can utilize', related_name='available_to_agents', to='main.agenttool'),
        ),
        migrations.AddField(
            model_name='genericagent',
            name='benchmark_metrics',
            field=models.ManyToManyField(help_text="Metrics used to benchmark this agent's performance", related_name='measured_on_agents', to='main.benchmarkmetric'),
        ),
        migrations.AddField(
            model_name='customagent',
            name='generic_agent',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='custom_agents', to='main.genericagent'),
        ),
        migrations.AddField(
            model_name='benchmarkrun',
            name='agent_definition',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='benchmark_runs', to='main.genericagent'),
        ),
        migrations.AddField(
            model_name='agentrun',
            name='agent',
            field=models.ForeignKey(help_text='The agent that was executed', on_delete=django.db.models.deletion.PROTECT, related_name='runs', to='main.genericagent'),
        ),
        migrations.AddField(
            model_name='historyevent',
            name='content_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='historyevent',
            name='secondary_content_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='secondary_events', to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='historyevent',
            name='user_profile',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, to='user.userprofile'),
        ),
        migrations.AddIndex(
            model_name='llmconfig',
            index=models.Index(fields=['name'], name='main_llmcon_name_2e61a8_idx'),
        ),
        migrations.AddIndex(
            model_name='llmconfig',
            index=models.Index(fields=['model_name'], name='main_llmcon_model_n_948738_idx'),
        ),
        migrations.AddIndex(
            model_name='llmconfig',
            index=models.Index(fields=['is_default'], name='main_llmcon_is_defa_69abc7_idx'),
        ),
        migrations.AddIndex(
            model_name='llmconfig',
            index=models.Index(fields=['is_evaluation'], name='main_llmcon_is_eval_755d92_idx'),
        ),
        migrations.AddConstraint(
            model_name='llmconfig',
            constraint=models.UniqueConstraint(condition=models.Q(('is_default', True), ('is_evaluation', False)), fields=('is_evaluation',), name='unique_default_non_evaluation_config'),
        ),
        migrations.AddConstraint(
            model_name='llmconfig',
            constraint=models.UniqueConstraint(condition=models.Q(('is_default', True), ('is_evaluation', True)), fields=('is_evaluation',), name='unique_default_evaluation_config'),
        ),
        migrations.AddField(
            model_name='genericagent',
            name='llm_config',
            field=models.ForeignKey(blank=True, help_text='The LLM configuration to use for this agent role. Cannot be null.', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='generic_agents', to='main.llmconfig'),
        ),
        migrations.AddField(
            model_name='benchmarkrun',
            name='llm_config',
            field=models.ForeignKey(blank=True, help_text='The LLM configuration used by the agent during this benchmark run.', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='benchmark_runs', to='main.llmconfig'),
        ),
        migrations.AddField(
            model_name='userfeedback',
            name='content_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='contenttypes.contenttype'),
        ),
        migrations.AddField(
            model_name='userfeedback',
            name='user_profile',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='user.userprofile'),
        ),
        migrations.AddField(
            model_name='wheelitem',
            name='activity_tailored',
            field=models.ForeignKey(help_text='Reference to the customized activity that will be assigned if this wheel segment is selected.', on_delete=django.db.models.deletion.CASCADE, related_name='wheel_items', to='activity.activitytailored'),
        ),
        migrations.AddField(
            model_name='wheelitem',
            name='wheel',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='main.wheel'),
        ),
        migrations.AddIndex(
            model_name='agentrecommendation',
            index=models.Index(fields=['status'], name='main_agentr_status_d534df_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrecommendation',
            index=models.Index(fields=['created_by_run'], name='main_agentr_created_9aa0ab_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrecommendation',
            index=models.Index(fields=['target_model'], name='main_agentr_target__9891d6_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrecommendation',
            index=models.Index(fields=['priority'], name='main_agentr_priorit_083788_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrecommendation',
            index=models.Index(fields=['created_at'], name='main_agentr_created_d8919f_idx'),
        ),
        migrations.AddIndex(
            model_name='agentmemory',
            index=models.Index(fields=['agent_role', 'user_profile', 'memory_key'], name='main_agentm_agent_r_85520a_idx'),
        ),
        migrations.AddIndex(
            model_name='agentmemory',
            index=models.Index(fields=['user_profile'], name='main_agentm_user_pr_820d85_idx'),
        ),
        migrations.AddIndex(
            model_name='agentmemory',
            index=models.Index(fields=['created_at'], name='main_agentm_created_3a3c29_idx'),
        ),
        migrations.AddIndex(
            model_name='agentmemory',
            index=models.Index(fields=['updated_at'], name='main_agentm_updated_d8dfef_idx'),
        ),
        migrations.AddIndex(
            model_name='agentmemory',
            index=models.Index(fields=['expires_at'], name='main_agentm_expires_794bca_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='agentmemory',
            unique_together={('agent_role', 'user_profile', 'memory_key')},
        ),
        migrations.AddIndex(
            model_name='agentmetric',
            index=models.Index(fields=['agent_run'], name='main_agentm_agent_r_7faf1d_idx'),
        ),
        migrations.AddIndex(
            model_name='agentmetric',
            index=models.Index(fields=['metric'], name='main_agentm_metric__e1752c_idx'),
        ),
        migrations.AddIndex(
            model_name='agentmetric',
            index=models.Index(fields=['timestamp'], name='main_agentm_timesta_99a419_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkscenario',
            index=models.Index(fields=['name'], name='main_benchm_name_b5478f_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkscenario',
            index=models.Index(fields=['agent_role'], name='main_benchm_agent_r_8ffd58_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkscenario',
            index=models.Index(fields=['is_active'], name='main_benchm_is_acti_38a284_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkscenario',
            index=models.Index(fields=['version'], name='main_benchm_version_eb2e1f_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkscenario',
            index=models.Index(fields=['is_latest'], name='main_benchm_is_late_13fc2a_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='benchmarkscenario',
            unique_together={('name', 'version')},
        ),
        migrations.AddIndex(
            model_name='betasignup',
            index=models.Index(fields=['email'], name='main_betasi_email_38d350_idx'),
        ),
        migrations.AddIndex(
            model_name='betasignup',
            index=models.Index(fields=['status'], name='main_betasi_status_05112d_idx'),
        ),
        migrations.AddIndex(
            model_name='betasignup',
            index=models.Index(fields=['created_at'], name='main_betasi_created_359e14_idx'),
        ),
        migrations.AddConstraint(
            model_name='betasignup',
            constraint=models.UniqueConstraint(fields=('email',), name='unique_beta_signup_email'),
        ),
        migrations.AlterUniqueTogether(
            name='customagent',
            unique_together={('user_profile', 'generic_agent')},
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['workflow_id'], name='main_agentr_workflo_d7697f_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['agent'], name='main_agentr_agent_i_7bd18f_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['user_profile'], name='main_agentr_user_pr_c66229_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['status'], name='main_agentr_status_36e159_idx'),
        ),
        migrations.AddIndex(
            model_name='agentrun',
            index=models.Index(fields=['started_at'], name='main_agentr_started_442aa8_idx'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['content_type', 'object_id'], name='main_histor_content_17e564_idx'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['secondary_content_type', 'secondary_object_id'], name='main_histor_seconda_1d95ff_idx'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['timestamp'], name='main_histor_timesta_aec51d_idx'),
        ),
        migrations.AddIndex(
            model_name='historyevent',
            index=models.Index(fields=['event_type'], name='main_histor_event_t_015083_idx'),
        ),
        migrations.AddIndex(
            model_name='genericagent',
            index=models.Index(fields=['role'], name='main_generi_role_f109cf_idx'),
        ),
        migrations.AddIndex(
            model_name='genericagent',
            index=models.Index(fields=['is_active'], name='main_generi_is_acti_cd58c1_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkrun',
            index=models.Index(fields=['scenario'], name='main_benchm_scenari_d7541e_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkrun',
            index=models.Index(fields=['agent_definition'], name='main_benchm_agent_d_8702f2_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkrun',
            index=models.Index(fields=['execution_date'], name='main_benchm_executi_bd4db9_idx'),
        ),
        migrations.AddIndex(
            model_name='benchmarkrun',
            index=models.Index(fields=['llm_config'], name='main_benchm_llm_con_96fc10_idx'),
        ),
    ]
