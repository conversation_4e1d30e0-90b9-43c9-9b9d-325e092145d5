# Generated by Django 5.2.1 on 2025-07-08 12:13

import django.core.serializers.json
import django.core.validators
import uuid
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='AgentGoal',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('description', models.TextField(help_text='Specific description of what the agent aims to achieve for the user.')),
                ('priority', models.IntegerField(help_text='Numerical priority ranking (1-5, with 1 being highest) to help agent prioritize competing goals.')),
                ('user_goal_id', models.UUIDField(help_text='Reference to the corresponding user goal this agent goal supports.')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
        migrations.CreateModel(
            name='AgentMemory',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('agent_role', models.Char<PERSON>ield(choices=[('mentor', 'Mentor Agent'), ('orchestrator', 'Orchestrator Agent'), ('resource', 'Resource & Capacity Management Agent'), ('engagement', 'Engagement & Pattern Analytics Agent'), ('psychological', 'Psychological Monitoring Agent'), ('strategy', 'Strategy Agent'), ('activity', 'Wheel/Activity Agent'), ('ethical', 'Ethical Oversight Agent'), ('dispatcher', 'Dispatcher Agent'), ('error_handler', 'Error Handler Agent')], help_text='The agent role this memory belongs to', max_length=20)),
                ('memory_key', models.CharField(help_text='Key identifying this specific memory item', max_length=255)),
                ('content', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The actual memory content stored as JSON')),
                ('confidence', models.FloatField(default=1.0, help_text='Confidence score (0.0-1.0) for this memory')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('last_accessed', models.DateTimeField(blank=True, null=True)),
                ('access_count', models.IntegerField(default=0)),
                ('expires_at', models.DateTimeField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Agent Memory',
                'verbose_name_plural': 'Agent Memories',
            },
        ),
        migrations.CreateModel(
            name='AgentMetric',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('value', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The measured value (could be number, boolean, string, etc.)')),
                ('context', models.JSONField(blank=True, default=dict, encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='Additional context about this measurement')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Agent Metric',
                'verbose_name_plural': 'Agent Metrics',
            },
        ),
        migrations.CreateModel(
            name='AgentRecommendation',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('target_model', models.CharField(help_text="The model this recommendation applies to (e.g., 'user.UserTraitInclination')", max_length=255)),
                ('target_instance_id', models.CharField(blank=True, help_text='ID of the specific instance to update, or null for new instances', max_length=255, null=True)),
                ('operation', models.CharField(choices=[('create', 'Create'), ('update', 'Update'), ('delete', 'Delete')], help_text='The operation to perform', max_length=20)),
                ('field_updates', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='For create/update operations, the field values to set')),
                ('rationale', models.TextField(help_text='Explanation for why this recommendation is being made')),
                ('evidence', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='Supporting evidence for this recommendation')),
                ('confidence', models.FloatField(default=0.8, help_text='Confidence score (0.0-1.0) for this recommendation')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('applied', 'Applied')], default='pending', help_text='Current status of this recommendation', max_length=20)),
                ('applied_at', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('priority', models.IntegerField(default=5, help_text='Priority from 1 (highest) to 10 (lowest)')),
            ],
            options={
                'verbose_name': 'Agent Recommendation',
                'verbose_name_plural': 'Agent Recommendations',
            },
        ),
        migrations.CreateModel(
            name='AgentRun',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('workflow_id', models.UUIDField(help_text='Identifier for the broader workflow this run is part of')),
                ('started_at', models.DateTimeField(help_text='When this agent run started')),
                ('completed_at', models.DateTimeField(blank=True, help_text='When this agent run completed', null=True)),
                ('input_data', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The input data provided to the agent')),
                ('output_data', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The output data produced by the agent', null=True)),
                ('initial_state', models.JSONField(encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The initial state passed to the agent')),
                ('final_state', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='The final state after agent execution', null=True)),
                ('memory_updates', models.JSONField(blank=True, encoder=django.core.serializers.json.DjangoJSONEncoder, help_text="Changes made to the agent's persistent memory", null=True)),
                ('status', models.CharField(choices=[('running', 'Running'), ('completed', 'Completed'), ('failed', 'Failed'), ('timeout', 'Timeout')], default='running', help_text='Current status of this agent run', max_length=20)),
                ('error_message', models.TextField(blank=True, help_text='Error message if this run failed', null=True)),
                ('tool_calls', models.JSONField(default=list, encoder=django.core.serializers.json.DjangoJSONEncoder, help_text='Records of all tool calls made during this run')),
            ],
            options={
                'verbose_name': 'Agent Run',
                'verbose_name_plural': 'Agent Runs',
            },
        ),
        migrations.CreateModel(
            name='AgentTool',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text="Unique code for this tool (e.g., 'user_traits_query')", max_length=50, unique=True)),
                ('name', models.CharField(help_text='Human-readable name for this tool', max_length=255)),
                ('description', models.TextField(help_text='Detailed description of what this tool does')),
                ('input_schema', models.JSONField(help_text='JSON Schema defining expected input format')),
                ('output_schema', models.JSONField(help_text='JSON Schema defining expected output format')),
                ('function_path', models.CharField(help_text="Fully qualified path to the tool function (e.g., 'apps.main.agents.tools.user_traits_handler')", max_length=255)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this tool is available for use')),
                ('allowed_agent_roles', models.JSONField(default=list, help_text='List of agent roles (from AgentRole) that can use this tool')),
                ('avg_response_time', models.FloatField(default=0.0, help_text='Average response time in seconds')),
                ('usage_count', models.IntegerField(default=0, help_text='Total number of times this tool has been used')),
                ('error_rate', models.FloatField(default=0.0, help_text='Percentage of tool invocations that resulted in errors')),
                ('definition_hash', models.CharField(blank=True, help_text="SHA-256 hash of the tool's definition (name, desc, path, schemas) for idempotency checks", max_length=64, null=True)),
            ],
            options={
                'verbose_name': 'Agent Tool',
                'verbose_name_plural': 'Agent Tools',
            },
        ),
        migrations.CreateModel(
            name='AppliedSeedingCommand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('command_name', models.CharField(help_text="Name of the management command that was applied (e.g., 'seed_db_40_envs').", max_length=255, unique=True)),
                ('applied_at', models.DateTimeField(auto_now_add=True, help_text='Timestamp when the command was successfully applied.')),
            ],
            options={
                'verbose_name': 'Applied Seeding Command',
                'verbose_name_plural': 'Applied Seeding Commands',
            },
        ),
        migrations.CreateModel(
            name='BenchmarkMetric',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(help_text='Unique code for this metric', max_length=50, unique=True)),
                ('name', models.CharField(help_text='Human-readable name for this metric', max_length=255)),
                ('description', models.TextField(help_text='Description of what this metric measures')),
                ('data_type', models.CharField(choices=[('numeric', 'Numeric'), ('boolean', 'Boolean'), ('categorical', 'Categorical'), ('duration', 'Duration')], help_text='Type of data this metric records', max_length=20)),
                ('unit', models.CharField(blank=True, help_text="Unit of measurement (e.g., 'seconds', 'percent')", max_length=50, null=True)),
                ('target_value', models.JSONField(blank=True, help_text='Target or ideal value for this metric', null=True)),
                ('valid_values', models.JSONField(blank=True, help_text='For categorical metrics, the list of valid values', null=True)),
                ('applicable_roles', models.JSONField(default=list, help_text='List of agent roles this metric applies to')),
            ],
            options={
                'verbose_name': 'Benchmark Metric',
                'verbose_name_plural': 'Benchmark Metrics',
            },
        ),
        migrations.CreateModel(
            name='BenchmarkRun',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('agent_version', models.CharField(help_text='Git hash or version identifier', max_length=40)),
                ('execution_date', models.DateTimeField(auto_now_add=True)),
                ('parameters', models.JSONField(default=dict, help_text='Benchmark parameters used for this run')),
                ('evaluator_llm_model', models.CharField(blank=True, help_text='The identifier of the LLM model used for semantic evaluation.', max_length=100, null=True)),
                ('runs_count', models.IntegerField(help_text='Number of times the scenario was executed')),
                ('mean_duration', models.FloatField(help_text='Mean execution time in milliseconds')),
                ('median_duration', models.FloatField(help_text='Median execution time in milliseconds')),
                ('min_duration', models.FloatField(help_text='Minimum execution time in milliseconds')),
                ('max_duration', models.FloatField(help_text='Maximum execution time in milliseconds')),
                ('std_dev', models.FloatField(help_text='Standard deviation of execution time in milliseconds')),
                ('success_rate', models.FloatField(help_text='Percentage of successful runs (0.0 to 1.0)')),
                ('llm_calls', models.IntegerField(default=0, help_text='Total number of LLM calls during the benchmark')),
                ('tool_calls', models.IntegerField(default=0, help_text='Total number of tool calls during the benchmark')),
                ('tool_breakdown', models.JSONField(default=dict, help_text='Count of calls per tool code')),
                ('tool_call_details', models.JSONField(default=dict, help_text='Detailed tool call information including mock vs real tool usage, call counts, and execution details')),
                ('memory_operations', models.IntegerField(default=0, help_text='Total number of memory operations')),
                ('semantic_score', models.FloatField(blank=True, help_text='Overall semantic quality score (0.0 to 1.0) from the primary evaluator, potentially averaged across dimensions.', null=True)),
                ('semantic_evaluation_details', models.JSONField(default=dict, help_text='Detailed overall semantic evaluation reasoning from the primary evaluator LLM.')),
                ('semantic_evaluations', models.JSONField(default=dict, help_text="Stores multi-dimensional evaluation results from multiple LLMs. Maps model name to {'dimensions': {'dimension_name': {'score': float, 'reasoning': str}}, 'overall_score': float, 'overall_reasoning': str, 'error': bool}.")),
                ('raw_results', models.JSONField(default=dict, help_text='Raw results from the underlying benchmark tool')),
                ('agent_communications', models.JSONField(default=dict, help_text="Detailed capture of agent-to-agent communications during workflow execution. Structure: {'workflow_id': str, 'agents': [{'agent': str, 'stage': str, 'input': dict, 'output': dict, 'timestamp': str, 'duration_ms': float}], 'state_transitions': [{'from_state': dict, 'to_state': dict, 'agent': str, 'timestamp': str}]}")),
                ('total_input_tokens', models.IntegerField(blank=True, help_text='Total number of input tokens processed by the LLM during the benchmark.', null=True)),
                ('total_output_tokens', models.IntegerField(blank=True, help_text='Total number of output tokens generated by the LLM during the benchmark.', null=True)),
                ('estimated_cost', models.DecimalField(blank=True, decimal_places=6, help_text='Estimated cost of the LLM usage for this benchmark run based on token counts and prices.', max_digits=10, null=True)),
                ('performance_p_value', models.FloatField(blank=True, help_text='P-value from statistical test comparing performance (e.g., duration) to the previous run.', null=True)),
                ('is_performance_significant', models.BooleanField(blank=True, help_text='Indicates if the performance difference compared to the previous run is statistically significant (e.g., p < 0.05).', null=True)),
                ('stage_performance_details', models.JSONField(blank=True, default=dict, help_text='Aggregated performance metrics (mean, median, std_dev, etc. in ms) for each profiled stage.')),
                ('last_response_length', models.IntegerField(blank=True, help_text="Length of the agent's last response in characters, if available.", null=True)),
            ],
            options={
                'verbose_name': 'Benchmark Run',
                'verbose_name_plural': 'Benchmark Runs',
                'ordering': ['-execution_date'],
            },
        ),
        migrations.CreateModel(
            name='BenchmarkScenario',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('agent_role', models.CharField(max_length=50)),
                ('input_data', models.JSONField()),
                ('workflow_type', models.CharField(blank=True, help_text="Type of workflow this scenario tests (e.g., 'wheel_generation', 'discussion')", max_length=50, null=True)),
                ('warmup_runs', models.PositiveIntegerField(default=1, help_text='Number of warmup runs before actual benchmarking')),
                ('benchmark_runs', models.PositiveIntegerField(default=3, help_text='Number of benchmark runs to perform')),
                ('timeout_seconds', models.PositiveIntegerField(blank=True, help_text='Timeout in seconds for scenario execution', null=True)),
                ('evaluation_template_id', models.PositiveIntegerField(blank=True, help_text='ID of the evaluation criteria template to use', null=True)),
                ('evaluation_template_name', models.CharField(blank=True, help_text='Name of the evaluation criteria template to use', max_length=100, null=True)),
                ('expected_quality_criteria', models.JSONField(blank=True, default=dict, help_text='Expected quality criteria for evaluation')),
                ('mock_tool_responses', models.JSONField(blank=True, default=dict, help_text='Mock responses for tool calls during testing')),
                ('user_profile_context', models.JSONField(blank=True, default=dict, help_text='User profile context for the scenario')),
                ('activity_context', models.JSONField(blank=True, default=dict, help_text='Activity-specific context for the scenario')),
                ('metadata', models.JSONField(default=dict, help_text='Additional metadata and legacy fields')),
                ('is_active', models.BooleanField(default=True)),
                ('version', models.PositiveIntegerField(default=1, help_text='Version number for this scenario definition.')),
                ('is_latest', models.BooleanField(default=True, help_text='Indicates if this is the latest version of the scenario.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Benchmark Scenario',
                'verbose_name_plural': 'Benchmark Scenarios',
            },
        ),
        migrations.CreateModel(
            name='BenchmarkTag',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Unique name for the tag (e.g., 'feature-x', 'regression', 'performance').", max_length=50, unique=True)),
                ('description', models.TextField(blank=True, help_text='Optional description for the tag.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Benchmark Tag',
                'verbose_name_plural': 'Benchmark Tags',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='BetaSignup',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('email', models.EmailField(help_text='Email address of the interested user', max_length=255)),
                ('message', models.TextField(blank=True, help_text='Optional message from the user about their interest', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When this signup request was submitted')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address of the user who submitted the request', null=True)),
                ('user_agent', models.TextField(blank=True, help_text='User agent string from the browser', null=True)),
                ('status', models.CharField(choices=[('pending', 'Pending Review'), ('contacted', 'Contacted'), ('invited', 'Invited to Beta'), ('registered', 'Registered as User'), ('declined', 'Declined')], default='pending', help_text='Current status of this beta signup request', max_length=20)),
                ('notes', models.TextField(blank=True, help_text='Internal notes about this signup request', null=True)),
                ('processed_at', models.DateTimeField(blank=True, help_text='When this request was processed by staff', null=True)),
            ],
            options={
                'verbose_name': 'Beta Signup',
                'verbose_name_plural': 'Beta Signups',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CustomAgent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='User-specific name of the customized agent.', max_length=255)),
                ('description', models.TextField(help_text="Tailored description of this agent instance's responsibilities for a specific user.")),
                ('instruction', models.TextField(help_text='Personalized instructions for the agent based on user profile and interaction history.')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='EvaluationContext',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, primary_key=True, serialize=False)),
                ('name', models.CharField(help_text="e.g., 'Stressed_Evening_WheelGen_Mentor'", max_length=100)),
                ('description', models.TextField(help_text='Description of this evaluation context scenario')),
                ('current_workflow_type', models.CharField(choices=[('wheel_generation', 'Wheel Generation'), ('activity_feedback', 'Activity Feedback'), ('discussion', 'Discussion'), ('onboarding', 'Onboarding'), ('post_spin', 'Post Spin'), ('pre_spin_feedback', 'Pre Spin Feedback')], help_text='Current workflow being executed', max_length=50)),
                ('workflow_stage', models.CharField(choices=[('initiation', 'Workflow Initiation'), ('agent_processing', 'Multi-Agent Processing'), ('result_delivery', 'Result Delivery'), ('user_response', 'Awaiting User Response'), ('error_recovery', 'Error Recovery')], default='agent_processing', help_text='Current stage in workflow', max_length=50)),
                ('agent_role_being_evaluated', models.CharField(choices=[('mentor', 'Mentor Agent'), ('orchestrator', 'Orchestrator Agent'), ('resource', 'Resource Agent'), ('engagement', 'Engagement Agent'), ('psychological', 'Psychological Agent'), ('strategy', 'Strategy Agent'), ('wheel_activity', 'Wheel/Activity Agent'), ('ethical', 'Ethical Agent')], help_text='Which agent is being evaluated', max_length=50)),
                ('trust_level_override', models.IntegerField(blank=True, help_text='Override trust level for evaluation (null = use UserProfile value)', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('mood_valence_override', models.FloatField(blank=True, help_text='Override mood valence for evaluation (null = use UserProfile value)', null=True, validators=[django.core.validators.MinValueValidator(-1.0), django.core.validators.MaxValueValidator(1.0)])),
                ('mood_arousal_override', models.FloatField(blank=True, help_text='Override mood arousal for evaluation (null = use UserProfile value)', null=True, validators=[django.core.validators.MinValueValidator(-1.0), django.core.validators.MaxValueValidator(1.0)])),
                ('reported_environment_override', models.CharField(blank=True, choices=[('home', 'Home'), ('work', 'Work'), ('public', 'Public Space'), ('transit', 'In Transit')], help_text='Override environment for evaluation', max_length=50, null=True)),
                ('stress_level_override', models.IntegerField(blank=True, help_text='Override stress level for evaluation', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('time_pressure_override', models.IntegerField(blank=True, help_text='Override time pressure for evaluation', null=True, validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('reported_time_availability_override', models.CharField(blank=True, choices=[('5_minutes', '5 minutes'), ('15_minutes', '15 minutes'), ('30_minutes', '30 minutes'), ('60_minutes', '60+ minutes')], help_text='Override time availability for evaluation', max_length=50, null=True)),
                ('reported_focus_override', models.CharField(blank=True, help_text="Override focus area for evaluation (e.g., 'creative', 'physical')", max_length=100, null=True)),
                ('previous_agent_outputs', models.JSONField(default=list, help_text='Simulated outputs from previous agents in workflow')),
                ('expected_next_agents', models.JSONField(default=list, help_text='Which agents should be called after this one')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Evaluation Context',
                'verbose_name_plural': 'Evaluation Contexts',
            },
        ),
        migrations.CreateModel(
            name='EvaluationCriteriaTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Unique name for this evaluation template (e.g., 'Clarity and Conciseness', 'Safety Check').", max_length=100, unique=True)),
                ('description', models.TextField(blank=True, help_text='Detailed description of what this template evaluates.')),
                ('workflow_type', models.CharField(blank=True, help_text='Specific workflow type this template is designed for (empty for all types).', max_length=50)),
                ('category', models.CharField(choices=[('semantic', 'Semantic Analysis'), ('quality', 'Quality Assessment'), ('phase', 'Phase-based Evaluation'), ('contextual', 'Contextual Evaluation'), ('custom', 'Custom Evaluation')], default='quality', help_text='Category of evaluation this template performs.', max_length=20)),
                ('criteria', models.JSONField(default=dict, help_text='Base evaluation criteria structure (e.g., dimension names to criteria lists).')),
                ('contextual_criteria', models.JSONField(default=dict, help_text='Contextual evaluation criteria that vary based on variables like trust_level, mood, environment.')),
                ('variable_ranges', models.JSONField(default=dict, help_text='Defines the ranges for contextual variables this template supports.')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this template is active and available for use.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Evaluation Criteria Template',
                'verbose_name_plural': 'Evaluation Criteria Templates',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='GenericAgent',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('role', models.CharField(choices=[('mentor', 'Mentor Agent'), ('orchestrator', 'Orchestrator Agent'), ('resource', 'Resource & Capacity Management Agent'), ('engagement', 'Engagement & Pattern Analytics Agent'), ('psychological', 'Psychological Monitoring Agent'), ('strategy', 'Strategy Agent'), ('activity', 'Wheel/Activity Agent'), ('ethical', 'Ethical Oversight Agent'), ('dispatcher', 'Dispatcher Agent'), ('error_handler', 'Error Handler Agent')], help_text='The specific role this agent fulfills in the system', max_length=20, unique=True)),
                ('version', models.CharField(default='1.0.0', help_text='Semantic version of this agent definition', max_length=20)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this agent definition is currently active')),
                ('description', models.TextField(help_text="Comprehensive description of this agent's purpose and responsibilities")),
                ('system_instructions', models.TextField(help_text="Base instructions defining the agent's core behavior and limitations")),
                ('input_schema', models.JSONField(help_text='JSON Schema defining the expected input format for this agent')),
                ('output_schema', models.JSONField(help_text='JSON Schema defining the required output format from this agent')),
                ('state_schema', models.JSONField(default=dict, help_text="JSON Schema defining the structure of this agent's state in Langgraph")),
                ('memory_schema', models.JSONField(default=dict, help_text="JSON Schema defining the structure of this agent's persistent memory")),
                ('read_models', models.JSONField(default=list, help_text='List of models this agent has read access to')),
                ('write_models', models.JSONField(default=list, help_text='List of models this agent has write access to')),
                ('recommend_models', models.JSONField(default=list, help_text='List of models this agent can create recommendations for')),
                ('langgraph_node_class', models.CharField(help_text="Fully qualified class name for this agent's Langgraph node implementation", max_length=255)),
                ('processing_timeout', models.IntegerField(default=30, help_text='Maximum allowed processing time in seconds before timeout')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Generic Agent',
                'verbose_name_plural': 'Generic Agents',
            },
        ),
        migrations.CreateModel(
            name='HistoryEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('event_type', models.CharField(max_length=100)),
                ('object_id', models.CharField(max_length=255)),
                ('secondary_object_id', models.CharField(blank=True, max_length=255, null=True)),
                ('details', models.JSONField(default=dict)),
            ],
        ),
        migrations.CreateModel(
            name='LLMConfig',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(help_text="Unique identifier for this LLM configuration (e.g., 'gpt-4-turbo-standard', 'claude-3-opus-creative').", max_length=100, unique=True)),
                ('model_name', models.CharField(help_text="The identifier of the LLM model used (e.g., 'gpt-4-1106-preview', 'claude-3-opus-20240229').", max_length=100)),
                ('temperature', models.FloatField(blank=True, help_text='The temperature setting for the LLM (controls randomness). Null means use default.', null=True)),
                ('input_token_price', models.DecimalField(blank=True, decimal_places=6, help_text='Price per (million) input token for this LLM (for cost estimation).', max_digits=10, null=True)),
                ('output_token_price', models.DecimalField(blank=True, decimal_places=6, help_text='Price per (million) output token for this LLM (for cost estimation).', max_digits=10, null=True)),
                ('is_evaluation', models.BooleanField(default=False, help_text='Indicates if this LLM configuration is for evaluation purposes.')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_default', models.BooleanField(default=False, help_text='If True, this configuration will be used as the default when no specific configuration is provided.')),
            ],
            options={
                'verbose_name': 'LLM Configuration',
                'verbose_name_plural': 'LLM Configurations',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='UserFeedback',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('feedback_type', models.CharField(max_length=255)),
                ('object_id', models.CharField(max_length=255)),
                ('created_on', models.DateTimeField(auto_now_add=True)),
                ('user_comment', models.TextField()),
                ('criticality', models.IntegerField()),
                ('context_data', models.JSONField(default=dict)),
                ('slack_payload', models.JSONField(default=dict)),
            ],
        ),
        migrations.CreateModel(
            name='Wheel',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Descriptive name of the wheel that may include user name and phase information.', max_length=255)),
                ('created_by', models.CharField(help_text='Identifier for the agent that generated this wheel.', max_length=255)),
                ('created_at', models.DateField(help_text='Date when the wheel was created for the user.')),
            ],
            options={
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='WheelItem',
            fields=[
                ('id', models.CharField(help_text='Unique identifier for this specific segment of the wheel.', max_length=50, primary_key=True, serialize=False)),
                ('percentage', models.FloatField(help_text='Probability weight between 0.0 and 100.0 that determines likelihood of selection based on user traits and trust level.')),
            ],
            options={
                'ordering': ['id'],
            },
        ),
    ]
