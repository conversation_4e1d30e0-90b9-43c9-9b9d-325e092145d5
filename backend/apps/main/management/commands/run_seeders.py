import logging
from django.core.management.base import BaseCommand
from django.core.management import call_command
from django.db import transaction, DatabaseError

# Assuming AppliedSeedingCommand is in apps.main.models
# Adjust the import path if necessary
from apps.main.models import AppliedSeedingCommand

logger = logging.getLogger(__name__)

# Define the sequence of seeding commands to run idempotently.
# Each item is a list: [command_name, arg1, arg2, ...]
# Use the full command string as the key for idempotency checks.
SEEDING_COMMANDS_WITH_ARGS = [
    ['seed_db_10_hexacos'],
    ['seed_db_20_limitations'],
    ['seed_db_30_domains'],
    ['seed_db_40_envs'],
    ['seed_db_45_resources'],
    ['seed_db_50_skill_system'],
    ['seed_db_55_generic_skills'],  # GenericSkill objects for user profiles
    ['seed_db_60_beliefs'],
    ['seed_db_70_activities'],
    ['cmd_register_tools'],
    ['seed_db_80_agents'],
    ['cmd_tool_connect', '--reset'],
    #['seed_db_phiphi'],
    ['seed_llm_configs'],
    # Add other commands here if they exist and need idempotency
    # Example: ['my_command', '--option', 'value']
]

class Command(BaseCommand):
    help = 'Runs necessary database seeding commands idempotently.'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("Starting idempotent seeding process..."))

        applied_count = 0
        skipped_count = 0
        error_count = 0

        for command_parts in SEEDING_COMMANDS_WITH_ARGS:
            # Use the full command string representation for idempotency check and logging
            command_key = " ".join(command_parts)
            command_name = command_parts[0]
            command_args = command_parts[1:]

            try:
                # Check if the command (with specific args) has already been applied
                already_applied = AppliedSeedingCommand.objects.filter(command_name=command_key).exists()

                if already_applied:
                    self.stdout.write(f"Skipping '{command_key}': Already applied.")
                    skipped_count += 1
                    continue

                self.stdout.write(f"Running '{command_key}'...")
                try:
                    # Use transaction.atomic to ensure the command and its tracking record are saved together
                    with transaction.atomic():
                        # Execute the command with proper argument handling
                        call_command(command_name, *command_args)

                        # Record that the command (with specific args) was successfully applied
                        AppliedSeedingCommand.objects.get_or_create(command_name=command_key)

                    self.stdout.write(self.style.SUCCESS(f"Successfully applied '{command_key}'."))
                    applied_count += 1

                except Exception as e:
                    logger.error(f"Error running seeding command '{command_key}': {e}", exc_info=True)
                    self.stderr.write(self.style.ERROR(f"Error running command '{command_key}': {e}"))
                    error_count += 1
                    # Decide if we should stop on error or continue
                    # For now, let's continue to try applying other seeders
                    # break # Uncomment to stop on first error

            except DatabaseError as db_err:
                 # Catch potential DB errors during the check or recording phase
                 logger.error(f"Database error while processing command '{command_key}': {db_err}", exc_info=True)
                 self.stderr.write(self.style.ERROR(f"Database error processing '{command_key}': {db_err}"))
                 error_count += 1
                 # Probably best to stop if the DB is having issues
                 break
            except Exception as outer_e:
                 # Catch any other unexpected errors
                 logger.error(f"Unexpected error processing command '{command_key}': {outer_e}", exc_info=True)
                 self.stderr.write(self.style.ERROR(f"Unexpected error processing '{command_key}': {outer_e}"))
                 error_count += 1
                 break # Stop on unexpected errors

        self.stdout.write(self.style.SUCCESS("\nSeeding process completed."))
        self.stdout.write(f"Summary: Applied={applied_count}, Skipped={skipped_count}, Errors={error_count}")

        if error_count > 0:
            self.stderr.write(self.style.ERROR("One or more seeding commands failed. Check logs for details."))
            # Optionally exit with a non-zero status code
            # import sys
            # sys.exit(1)
