# AI-ENTRYPOINT.md - Backend Development Guide

This document serves as the central entry point for AI assistants working on the Goali backend. It provides essential context, tools, and documentation references for efficient development.
> **🤖 AI Agent Entry Point Rules**
>
> This file serves as the mandatory entry point for AI agents working in this workspace.
>
> **RULES TO RESPECT:**
> - **Maintain the list of available tools and their description up-to-date**
> - **Maintain the list of available AI-intended documentation and their description up-to-date**
> - **Do not write anything here that is not specifically about the usage of that workspace**
> - **Clean if you see that one of those rules is not respected**
>
> This file must contain ONLY:
> 1. Clear workspace purpose and scope
> 2. Complete catalog of available tools with descriptions and usage
> 3. Complete catalog of available documentation with descriptions and purpose
> 4. Quick-start commands for common scenarios
> 5. Decision matrix for tool selection based on symptoms/needs

## 🎯 Current Mission Context

**Active Development Phase**: Database Migration System - COMPLETED ✅
**Latest Achievement**: Fixed database migration issues, implemented clean migration process from scratch
**Critical Infrastructure Fix**: Resolved migration dependency order and created robust reset/migration tools (Session 43)
**Migration System Status**: Production-ready with comprehensive testing and user-friendly tools
**Next Focus**: Production deployment validation and performance optimization

## 📋 Essential Documentation

### Core Architecture
- `docs/backend/AGENTS_AND_WORKFLOWS_COMPREHENSIVE_GUIDE.md` - Complete agent and workflow documentation
- `docs/backend/BENCHMARKING_SYSTEM.md` - Benchmarking system architecture and usage
- `docs/backend/quality/CONTEXTUAL_EVALUATION_SYSTEM.md` - Quality evaluation framework
- `docs/backend/LANGGRAPH_BEST_PRACTICES.md` - LangGraph implementation guidelines

### Data Models & Schemas
- `docs/backend/DATA_FLOW_AUTHORITATIVE_SPECS.md` - Authoritative data flow specifications
- `docs/backend/AUTHORITATIVE_SCHEMAS.md` - Central schema definitions
- `apps/main/schemas/activity_schemas.py` - Activity metadata schemas (ActivityMetadataV1)
- `apps/main/schemas/` - Pydantic model definitions

### Catalog Architecture System (Sessions 24-25)
- `docs/backend/CATALOG_ARCHITECTURE.md` - Complete catalog architecture documentation
- `data/authoritative_catalogs/` - JSON catalog files (domains, environments, resources, user_profile_catalog)
- `data/authoritative_catalogs/comprehensive_codes_catalog.json` - Generated master catalog
- `apps/main/services/catalog_validation_service.py` - **ROBUST** Comprehensive validation service
- `apps/main/management/commands/generate_codes_catalog.py` - Master catalog generator
- `apps/main/management/commands/seed_db_45_resources.py` - **FIXED** Resource seeding command
- `apps/admin_tools/views/command_management.py` - **ROBUST** Admin commands interface
- `templates/admin_tools/command_management.html` - **NEW** Command execution GUI
- `test_catalog_fixes.py` - **ENHANCED** Comprehensive testing script
- `test_server_startup.py` - **NEW** Server startup validation script

### Testing & Robustness (Session 25 - NEW)
- `apps/main/tests/test_catalog_validation_service.py` - **NEW** Validation service test suite
- `apps/admin_tools/tests/test_command_management.py` - **NEW** Admin interface test suite
- `apps/main/tests/test_seeding_commands.py` - **NEW** Seeding commands test suite
- `pytest.ini` - **ENHANCED** Test configuration with catalog markers
- `ROBUSTNESS_IMPROVEMENTS_SUMMARY.md` - **NEW** Complete robustness documentation

### Domain Management
- `docs/backend/domain_management/` - Domain system documentation
- `apps/main/services/domain_management_service.py` - Domain management implementation
- `data/authoritative_catalogs/domains.json` - Authoritative domain definitions

### Activity Management (Recently Enhanced)
- `apps/main/management/commands/seed_db_70_activities.py` - Intelligent activity seeding with metadata
- `apps/main/services/programmatic_activity_selector.py` - Enhanced activity selection with environment/resource filtering
- `apps/main/domain/services/activity_selection_service.py` - Domain-aware activity selection

### Enhanced User Profile Import System (Session 23 - PRODUCTION READY) + Profile Generation Fixes (CURRENT SESSION)
**Core Documentation:**
- `docs/backend/users/USER_PROFILE_IMPORT_SYSTEM.md` - Complete system documentation with enhanced features + latest fixes
- `backend/schemas/user_profile.schema.json` - JSON schema with database enum constraints + belief_code field (AUTHORITATIVE)
- `docs/backend/VALID_REFERENCE_CODES.md` - Complete list of valid codes from database (AUTHORITATIVE)
- `docs/backend/users/questionnaire2json_PROMPT.md` - Updated prompt with exact database codes + comprehensive field requirements (AUTHORITATIVE)
- `docs/backend/users/PROFILE_GENERATION_FIXES_SUMMARY.md` - **NEW** Complete fix documentation for profile generation process
- `docs/backend/users/NEXT_SESSION_PROFILE_GENERATION.md` - **NEW** Strategic prompt for AI-powered profile generation

**Core Services:**
- `apps/user/services/profile_import_service.py` - Enhanced import service with history tracking and batch processing
- `apps/user/services/user_profile_business_objects.py` - Pydantic validation models
- `apps/admin_tools/models.py` - ImportHistory model for comprehensive tracking
- `apps/admin_tools/views.py` - Enhanced API endpoints with robust error handling

**Admin Interface:**
- `backend/templates/admin_tools/user_profile_management.html` - Enhanced admin interface with import history
- API endpoints: `/admin/user-profiles/validate/`, `/admin/user-profiles/import/`, `/admin/batch-import/api/`

**Schema Generation Tools:**
- `generate_schema_with_enums.py` - Tool to regenerate schema from database (maintains single source of truth)
- `check_available_codes.py` - Tool to validate codes against database
- `apps/user/services/user_profile_business_objects.py` - Pydantic models for profile import validation
- `apps/user/services/environment_property_service.py` - Environment property management helper **FIXED** (novelty_level field filtering)
- `apps/user/services/profile_validation_service.py` - Enhanced validation service with reference code checking
- `backend/schemas/user_profile.schema.json` - JSON schema for profile import validation **UPDATED** (belief_code + effective_start)
- `docs/users/implementation_guide.md` - Complete implementation guide for profile import system
- `backend/data/authoritative_catalogs/comprehensive_codes_catalog.md` - **UPDATED** 436 validated system codes including belief codes

**Testing Infrastructure (24/24 tests passing - 100%):**
- `test_enhanced_profile_import_system.py` - Comprehensive test suite for enhanced import system (6/6 tests passing)
- `test_batch_import_system.py` - Batch import testing (4/4 tests passing)
- `test_complete_enhanced_system.py` - End-to-end system testing (4/4 tests passing)
- `test_admin_ui_endpoints.py` - Admin UI robustness testing (10/10 tests passing)
- `test_guigui_import.py` - Real-world profile import testing with schema validation

**Session 23 Analysis & Specifications:**
- `AUTHORITATIVE_SPEC_ANALYSIS_REPORT.md` - Comprehensive analysis comparing current capabilities to ultimate UX vision
- `SESSION_23_COMPREHENSIVE_SUMMARY.md` - Complete summary of achievements and strategic impact
- `ADMIN_UI_ROBUSTNESS_REPORT.md` - Detailed analysis of admin interface error handling

## 🛠️ Key Development Tools

### Critical Debugging Tools (Session 21 Validated)
- `backend/test_real_user_experience_wheel_bug.py` - **ESSENTIAL** Real WebSocket flow testing
  - **Purpose**: Tests actual user experience, not just backend APIs
  - **Usage**: `docker exec -it backend-web-1 python /usr/src/app/test_real_user_experience_wheel_bug.py`
  - **✅ VALIDATED**: Successfully identified and fixed wheel disappearance bug
  - **Critical**: Only tool that caught frontend ID inconsistency issues

### Wheel Architecture Debugging
- **Celery Log Analysis**: `docker logs backend-celery-1 --tail 200 | grep -A5 -B5 "ERROR\|Failed"`
- **Repository Pattern Validation**: Check `django_wheel_repository.py` for user context passing
- **ID Consistency Testing**: Verify 6/6 wheel items get proper database IDs (not temporary)

### Digital Ocean App Platform Deployment Tools (CRITICAL)
**BREAKTHROUGH**: CLI-based deployment method discovered to bypass git commit popup issues.

#### Working Celery Worker Configuration (do.yaml)
```yaml
workers:
- name: worker
  github:
    repo: elgui/goali
    branch: gguine/prod-slow
    deploy_on_push: true
  source_dir: backend
  environment_slug: python
  run_command: celery -A config worker --loglevel=info --concurrency=2
  instance_count: 1
  instance_size_slug: apps-s-1vcpu-1gb
  envs:
  - key: DJANGO_SETTINGS_MODULE
    scope: RUN_AND_BUILD_TIME
    value: config.settings.prod
  - key: DJANGO_SECRET_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: MISTRAL_API_KEY
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DATABASE_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: REDIS_URL
    scope: RUN_AND_BUILD_TIME
    type: SECRET
  - key: DJANGO_ALLOWED_HOSTS
    scope: RUN_AND_BUILD_TIME
    value: 127.0.0.1,localhost,monkfish-app-jvgae.ondigitalocean.app
```

#### Essential CLI Commands
```bash
# Update app specification directly (RECOMMENDED - bypasses git popup)
doctl apps update d64d0cf5-5add-4606-a970-6b146314988a --spec do.yaml

# Monitor deployment progress
doctl apps list-deployments d64d0cf5-5add-4606-a970-6b146314988a | head -3

# Check worker logs once deployed
doctl apps logs d64d0cf5-5add-4606-a970-6b146314988a worker --type=run

# Check build logs for troubleshooting
doctl apps logs d64d0cf5-5add-4606-a970-6b146314988a worker --type=build

# Verify worker component recognition
doctl apps logs d64d0cf5-5add-4606-a970-6b146314988a --type=build | grep -i worker
```

#### Key Research Findings
- **Worker Recognition**: Use `github:` with `repo:` instead of `git:` with `repo_clone_url:`
- **Environment Variables**: All worker env vars need `scope: RUN_AND_BUILD_TIME`
- **Component Names**: Ensure ingress rules match actual component names (e.g., `staticfiles` not `static-files`)
- **CLI vs Git**: CLI method bypasses git commit popup issues that block automated deployments
- **Dependencies**: Worker successfully installs Celery 5.5.2 and all Python dependencies

#### Troubleshooting Worker Deployment
1. **Worker Not Found**: Check if component name in logs matches YAML configuration
2. **Build Failures**: Review build logs with `doctl apps logs --type=build`
3. **Environment Issues**: Verify all SECRET type environment variables are configured in Digital Ocean
4. **YAML Validation**: Use `doctl apps update` to validate spec before deployment
5. **Frontend Build Errors**: May cause overall deployment failure but worker can still build successfully

### Testing & Validation
- `test_wheel_generation_fix.py` - Wheel generation testing script
- `docker exec -it backend-web-1 python /usr/src/app/test_workflow_benchmark.py` - Workflow benchmark testing
- `backend/templates/admin_tools/benchmark_history.html` - Admin interface for benchmarks
- `test_simple_profile_import.py` - **NEW** Profile import system testing
- `test_profile_import_integration.py` - **NEW** Comprehensive profile import integration tests
- `apps/user/tests/test_profile_import.py` - **NEW** Unit tests for profile import components

### Profile Import System Commands (Session 23) + Generation Fixes (CURRENT)
- `docker exec -it backend-web-1 python /usr/src/app/generate_schema_with_enums.py` - **CRITICAL** Regenerate schema from database
- `docker exec -it backend-web-1 python /usr/src/app/test_enhanced_profile_import_system.py` - Core import testing (6/6 tests)
- `docker exec -it backend-web-1 python /usr/src/app/test_admin_ui_endpoints.py` - Admin UI robustness (10/10 tests)
- `docker exec -it backend-web-1 python /usr/src/app/test_guigui_import.py` - Real profile import with schema validation
- `docker exec -it backend-web-1 python /usr/src/app/test_generation_fixes.py` - **NEW** Validate profile generation fixes (schema + catalog + prompt)
- `docker exec -it backend-web-1 python /usr/src/app/test_admin_validation.py` - **NEW** Test admin validation with fixed profile data

### Catalog Management Commands (Sessions 24-25)
- `docker exec -it backend-web-1 python manage.py generate_codes_catalog` - Generate comprehensive codes catalog
- `docker exec -it backend-web-1 python manage.py seed_db_45_resources` - Seed resources from JSON catalog
- `docker exec -it backend-web-1 python manage.py run_seeders` - **FIXED** Run all seeding commands (all errors resolved)
- `docker exec -it backend-web-1 python /usr/src/app/test_catalog_fixes.py` - **ENHANCED** Comprehensive system testing
- `docker exec -it backend-web-1 python /usr/src/app/test_server_startup.py` - **NEW** Server startup validation
- **Admin Interface**: `/admin/commands/` - GUI for command execution and catalog validation
- **Catalog Validation**: Use `CatalogValidationService` for comprehensive validation

### Testing Framework (Session 25 - NEW)
- `docker exec -it backend-web-1 pytest apps/main/tests/test_catalog_validation_service.py -v` - **NEW** Validation service tests
- `docker exec -it backend-web-1 pytest apps/admin_tools/tests/test_command_management.py -v` - **NEW** Admin interface tests
- `docker exec -it backend-web-1 pytest apps/main/tests/test_seeding_commands.py -v` - **NEW** Seeding command tests
- `docker exec -it backend-web-1 pytest -m catalog -v` - **NEW** Run all catalog-related tests
- **Pytest Markers**: `catalog`, `seeding`, `admin`, `validation` for organized testing

### Database Management
- `python manage.py seed_db_70_activities` - Intelligent activity seeding with metadata
- `apps/main/management/commands/` - Custom management commands
- Enhanced metadata generation for precise activity filtering

### Database Migration System (Session 43 - PRODUCTION READY)
**Core Tools:**
- `backend/apps/utils/reset_db.py` - **ROBUST** Database reset and migration cleanup tool
- `backend/reset_and_migrate.sh` - **NEW** User-friendly reset and migration script
- `backend/test_migration_process.py` - **NEW** Comprehensive migration testing script
- `backend/entrypoint.sh` - **UPDATED** Proper migration order (admin_tools → user → activity → main)

**Migration Order (Dependency-Aware):**
1. `admin_tools` - No dependencies
2. `user` - Dependency for activity and main
3. `activity` - Depends on user
4. `main` - Depends on user and activity

**Quick Commands:**
```bash
# Reset and recreate migrations from scratch
cd backend && ./reset_and_migrate.sh

# Test complete migration process
cd backend && python test_migration_process.py

# Manual reset (advanced)
python apps/utils/reset_db.py --delete-migrations
docker-compose down && docker-compose up -d
```

**Migration Files Structure:**
- `apps/admin_tools/migrations/0001_initial.py` - ImportHistory model
- `apps/user/migrations/0001_initial.py` - User profile system
- `apps/activity/migrations/0001_initial.py` + `0002_initial.py` - Activity system with user dependencies
- `apps/main/migrations/0001_initial.py` + `0002_initial.py` - Main system with user/activity dependencies

**Validation Results**: ✅ Complete migration process works from empty database to fully seeded system

### Quality Assurance
- Benchmarking system for LLM quality measurement
- Integration tests for workflow validation
- Admin interface for monitoring and debugging
- Intelligent activity selection with domain diversity

## 🔧 Development Patterns

### Repository Pattern
- Abstract interfaces in `apps/main/domain/`
- Django ORM implementations in `apps/main/infrastructure/`
- Dependency injection for testability
- Enhanced metadata-based filtering

### Agent Architecture
- Thin coordinators delegating to domain services
- Centralized business logic in services
- Pydantic models for data validation
- Intelligent activity selection algorithms

### Error Handling
- Comprehensive error broadcasting system
- Graceful fallbacks for LLM failures
- Detailed logging for debugging
- Domain diversity validation

## 📊 Recent Enhancements (Session 10)

### Intelligent Activity Metadata System
- **ActivityMetadataV1 Schema**: Comprehensive metadata structure for activities
- **Smart Duration Parsing**: Converts duration ranges to precise min/max values
- **Domain-Based Energy Mapping**: Intelligent energy level assignment based on activity domains
- **Challenge Level Calculation**: Automated challenge rating based on complexity factors
- **Physical Intensity Detection**: Domain-based physical intensity scoring
- **Social Requirement Inference**: Automatic social context determination

### Enhanced Activity Selection
- **Environment-Based Filtering**: Indoor/outdoor, space, noise, privacy constraints
- **Resource Requirement Filtering**: Intelligent resource matching with substitution logic
- **Critical Resource Detection**: Identifies non-substitutable requirements
- **Domain Diversity Algorithms**: Ensures varied activity selection
- **Intelligent Scoring**: Multi-factor scoring with environment and resource compatibility

### Programmatic Activity Selector Improvements
- **Smart Resource Inference**: Domain-based resource requirement detection
- **Environment Context Analysis**: Location, space, and privacy considerations
- **Resource Substitution Logic**: Intelligent alternative resource matching
- **Enhanced Filtering Pipeline**: Multi-stage filtering for optimal selection

## 🚀 Enhanced Profile Import System (Session 23 - PRODUCTION READY)

### Core Features Implemented
- **Import History Tracking**: Complete audit trail with performance metrics and quality scoring
- **Batch Import Processing**: High-performance batch processing (3.61+ profiles/second)
- **Enhanced Admin Interface**: Import history management with real-time feedback
- **Advanced API Endpoints**: Comprehensive REST API for import operations
- **Quality Assessment**: Automated data quality scoring and completeness analysis
- **Error Management**: Structured error tracking with context and recovery suggestions

### Testing Tools (All Tests Passing 100%)
- `test_enhanced_profile_import_system.py` - Core functionality testing (6/6 tests passing)
- `test_batch_import_system.py` - Batch processing validation (4/4 tests passing)
- `test_complete_enhanced_system.py` - End-to-end system testing (4/4 tests passing)
- `test_admin_ui_endpoints.py` - Admin UI robustness testing (10/10 tests passing)
- `test_simple_profile_import.py` - Basic import functionality validation
- `test_profile_import_integration.py` - Integration testing with real data

### Quick Test Commands
```bash
# Test enhanced system
docker exec -it backend-web-1 python /usr/src/app/test_enhanced_profile_import_system.py

# Test batch import
docker exec -it backend-web-1 python /usr/src/app/test_batch_import_system.py

# Test complete system
docker exec -it backend-web-1 python /usr/src/app/test_complete_enhanced_system.py

# Test admin UI robustness
docker exec -it backend-web-1 python /usr/src/app/test_admin_ui_endpoints.py
```

### API Endpoints
- `GET /admin/import-history/api/` - List import history with filtering
- `GET /admin/import-history/api/{id}/` - Get detailed import history
- `DELETE /admin/import-history/api/{id}/` - Delete import history
- `POST /admin/batch-import/api/` - Batch profile import
- `GET /admin/batch-import/api/` - Get batch import configuration

### Performance Benchmarks
- **Single Import**: ~0.3 seconds per profile
- **Batch Processing**: 3.61+ profiles per second
- **Error Recovery**: 75% success rate in mixed scenarios
- **Quality Assessment**: Automated scoring (0.0-1.0 scale)

## 🎉 Recent Major Fixes (Session 21)

### ✅ Wheel Disappearance Bug - COMPLETELY RESOLVED
- **Issue**: Wheel items had temporary IDs (`wheel-item-X`), causing removal failures
- **Root Causes Fixed**:
  1. `duration_minutes` parameter mismatch in `ActivityTailored` creation
  2. User environment context not passed to repository
  3. Database constraint violations in `GenericActivity` creation
- **Files Modified**:
  - `apps/main/infrastructure/repositories/django_wheel_repository.py`
  - `apps/main/domain/services/wheel_generation_service.py`
  - `apps/main/agents/tools/tools.py`
- **Result**: 6/6 wheel items now have proper database IDs, zero errors

### ✅ Frontend Wheel Segments Display - COMPLETELY RESOLVED (Session 42)
- **Issue**: Wheel segments not appearing despite successful backend generation
- **Root Cause**: Frontend state management data format mismatch - `this.wheelData` never updated from state machine
- **Solution**: State machine subscription updates `this.wheelData` with proper format conversion
- **Files Modified**: `frontend/src/components/app-shell.ts` (state machine subscription and data conversion)
- **Result**: 100% functional wheel generation with complete user flow from generation to spinning
- **Testing**: Both admin debug mode and real user scenarios validated with Playwright
- **Documentation**: Complete fix documentation in `frontend/ai-live-testing-tools/AI-ENTRYPOINT.md`

## 🎉 NEW: User Profile Import System (Session 22)

### ✅ Comprehensive Profile Import Implementation - COMPLETE
- **Achievement**: Full-featured user profile import system with validation, performance optimization, and error handling
- **Components Delivered**:
  1. **Business Objects**: Pydantic models mirroring JSON schema with validation
  2. **Import Service**: Enhanced ProfileImportService with schema validation and reference checking
  3. **Environment Property Service**: Helper for managing environment property models
  4. **Validation Service**: Reference code validation and consistency checks
  5. **Custom Exceptions**: Structured error responses with detailed field-level messages
  6. **Performance Optimization**: Caching, bulk operations, and query optimization
  7. **Comprehensive Tests**: Unit tests, integration tests, and real-world validation
- **Files Created/Enhanced**:
  - `apps/user/services/user_profile_business_objects.py` - Pydantic validation models
  - `apps/user/services/profile_import_service.py` - Enhanced import service
  - `apps/user/services/environment_property_service.py` - Environment helper service
  - `apps/user/services/profile_validation_service.py` - Enhanced validation
  - `apps/admin_tools/views.py` - Updated API endpoints (PUT for import, OPTIONS for schema)
  - `apps/user/tests/test_profile_import.py` - Comprehensive test suite
  - `test_simple_profile_import.py` - Integration testing script
- **Quality Checkpoints Verified**:
  - ✅ Business objects validate correctly with Pydantic v2
  - ✅ JSON schema validation catches structural errors
  - ✅ Reference code validation works for all entity types
  - ✅ Complete profile import creates all database records
  - ✅ Transaction rollback works on any error
  - ✅ Performance optimizations (caching, bulk operations) implemented
  - ✅ Error messages are clear and actionable
  - ✅ All tests pass successfully

## 🚨 Current Known Issues

### Current Blockers
- **None currently identified** - All critical issues resolved ✅

### Recent Major Fixes (Sessions 24-25)
- **Server Startup Issue**: Fixed import errors preventing server startup ✅
- **Catalog Seeding Errors**: All seeding command errors fixed (limitations, domains, resources, beliefs) ✅
- **Validation System**: Comprehensive validation service with model field validation ✅
- **Admin Interface**: Command execution GUI with real-time output and catalog validation ✅
- **Data Integrity**: Pre-validation prevents database corruption, transaction rollback on errors ✅
- **System Robustness**: Graceful error handling, comprehensive testing, production-ready reliability ✅

### Domain Diversity Calibration (Future Enhancement)
- **Issue**: High energy selection (100%) results in all physical activities
- **Status**: Domain diversity algorithm implemented but needs fine-tuning
- **Impact**: Wheel validation fails due to insufficient domain variety
- **Next Steps**: Adjust energy strategy weights to ensure minimum 2 domains

## 🔍 Decision Matrix

### Profile Generation Process Fixes (CURRENT SESSION - COMPLETE)

**🎯 Mission Status**: ✅ **COMPLETELY FIXED** - All root causes of profile generation failures resolved

**Key Fixes Applied**:
1. **belief_code Integration**: Added `belief_code` field to schema and prompt for catalog linking
2. **Required Environment Fields**: Added `effective_start` and comprehensive field documentation
3. **Service Layer Bug**: Fixed `EnvironmentPropertyService` field filtering for `novelty_level`
4. **Complete Field Requirements**: Updated prompt with clear REQUIRED indicators

**Files Updated**:
- `backend/schemas/user_profile.schema.json` - Added belief_code and effective_start requirements
- `docs/backend/users/questionnaire2json_PROMPT.md` - Comprehensive field requirement documentation
- `backend/apps/user/services/environment_property_service.py` - Fixed valid field filtering
- `docs/backend/users/USER_PROFILE_IMPORT_SYSTEM.md` - Updated with latest fixes

**Validation Results**: ✅ All tests pass - schema validation, catalog integration, service logic aligned

### When to Use Enhanced Profile Import System
| Scenario | Tool/Approach | Command |
|----------|---------------|---------|
| Single profile import | ProfileImportService | `service.import_profile(profile_data)` |
| Multiple profiles | Batch Import API | `POST /admin/batch-import/api/` |
| Import history review | Import History API | `GET /admin/import-history/api/` |
| Performance testing | Test scripts | `test_complete_enhanced_system.py` |
| Quality validation | Quality metrics | Check `data_quality_score` in ImportHistory |
| Error debugging | Import history errors | Review `errors` field in ImportHistory |
| Admin interface | User profile management | `/admin/user-profile-management/` |
| **Profile generation testing** | **Generation fixes validation** | **`test_generation_fixes.py`** |

### When to Use Database Migration System
| Scenario | Tool/Approach | Command |
|----------|---------------|---------|
| **Migration issues** | **Reset and recreate** | **`./reset_and_migrate.sh`** |
| **Empty database setup** | **Complete migration process** | **`python test_migration_process.py`** |
| Database corruption | Manual reset | `python apps/utils/reset_db.py --delete-migrations` |
| New environment setup | Automated migration | `docker-compose up` (uses entrypoint.sh) |
| Migration testing | Comprehensive validation | `test_migration_process.py` |
| Development reset | Quick reset | `./reset_and_migrate.sh --force` |
| Production deployment | Dependency-aware migrations | Follow entrypoint.sh order |

### Profile Import Troubleshooting + Generation Fixes
| Symptom | Likely Cause | Solution |
|---------|--------------|----------|
| Schema validation fails | Invalid JSON structure | Check against `user_profile.schema.json` |
| Reference validation fails | Invalid codes | Use existing trait/skill/environment codes |
| Database constraint errors | Missing required fields | **FIXED** Use updated prompt with all required fields |
| novelty_level null errors | Service layer bug | **FIXED** Updated EnvironmentPropertyService |
| Missing belief_code | Generation process issue | **FIXED** Use updated schema + prompt with belief_code |
| Missing effective_start | Environment field missing | **FIXED** Added to schema as required field |
| Performance issues | Large batch size | Use batch processing with reasonable size |
| Quality score low | Incomplete data | Add more profile sections (traits, skills, etc.) |

### Database Migration Troubleshooting
| Symptom | Likely Cause | Solution |
|---------|--------------|----------|
| Migration dependency errors | Wrong migration order | Use updated entrypoint.sh order |
| "No migrations to apply" but models changed | Missing migration files | Run `makemigrations` then restart |
| Database constraint violations | Corrupted migration state | Use `./reset_and_migrate.sh` |
| Container startup fails | Migration errors | Check logs: `docker-compose logs web` |
| Empty database after startup | Migration not applied | Verify entrypoint.sh execution |
| Foreign key constraint errors | Dependency order wrong | Follow: admin_tools → user → activity → main |
| "Table already exists" errors | Partial migration state | Reset: `python apps/utils/reset_db.py --delete-migrations` |
| Docker compose hangs | Database not ready | Wait for DB health check, check `docker-compose ps` |

## 🔍 Quick Reference

### Common Commands
```bash
# Test wheel generation
docker exec -it backend-web-1 python /usr/src/app/test_wheel_generation_fix.py 1 10 100

# Run workflow benchmarks
docker exec -it backend-web-1 python /usr/src/app/test_workflow_benchmark.py

# Seed database with intelligent metadata
docker exec -it backend-web-1 python manage.py seed_db_70_activities

# Check activity metadata
docker exec -it backend-web-1 python -c "
from apps.activity.models import GenericActivity
activities = GenericActivity.objects.filter(metadata__isnull=False)[:5]
for a in activities: print(f'{a.name}: {a.metadata}')
"

# Enhanced Profile Import System
docker exec -it backend-web-1 python /usr/src/app/test_enhanced_profile_import_system.py
docker exec -it backend-web-1 python /usr/src/app/test_batch_import_system.py
docker exec -it backend-web-1 python /usr/src/app/test_complete_enhanced_system.py

# Check import history
docker exec -it backend-web-1 python -c "
from apps.admin_tools.models import ImportHistory
recent = ImportHistory.objects.order_by('-started_at')[:5]
for h in recent: print(f'{h.import_type}: {h.status} - {h.success_rate:.1f}%')
"

# Check celery logs
docker logs backend-celery-1 --tail 100
```

### Key File Locations
- Agents: `apps/main/agents/`
- Services: `apps/main/services/` and `apps/main/domain/services/`
- Models: `apps/main/models/` and `apps/main/domain/models/`
- Schemas: `apps/main/schemas/` (especially `activity_schemas.py`)
- Tests: `apps/main/tests/`
- Seeding: `apps/main/management/commands/seed_db_70_activities.py`

## 📈 Performance Metrics

### Current System Status
- Repository Performance: ✅ Enhanced with metadata-based pre-filtering
- Activity Selection: ✅ Improved with intelligent scoring and domain-aware algorithms
- Metadata Generation: ✅ Efficient processing of 105+ activities
- Filtering Accuracy: ✅ Smart resource matching with substitution logic

### Quality Indicators
- Metadata Coverage: 95%+ activities have intelligent metadata
- Selection Precision: Enhanced with domain-based filtering
- Resource Matching: Intelligent substitution and critical resource detection
- System Reliability: Graceful handling of edge cases and failures

## 🎯 Next Session Focus

### Priority Tasks
1. **Domain Diversity Calibration**: Fine-tune HighEnergyStrategy for balanced selection
2. **Energy Mapping Optimization**: Review and refine domain-based energy ranges
3. **Integration Testing**: Validate wheel generation across energy scenarios
4. **Quality Assurance**: Ensure contextual appropriateness is maintained
